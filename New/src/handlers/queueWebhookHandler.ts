import type { Context } from "hono";
import { getDb } from "@/database";
import { webhookQueue } from "@/database/schema";
import { logInfo } from "@/utils/logger";

export async function queueCCWebhookHandler(c: Context): Promise<Response> {
	const timestamp = new Date().toISOString();

	try {
		logInfo("Received CC webhook for queue");

		const payload = await c.req.json();
		
		const db = getDb();
		const webhookId = crypto.randomUUID();
		
		// Validate required fields
		if (!payload.id) {
			throw new Error("CC webhook payload missing required 'id' field");
		}

		await db.insert(webhookQueue).values({
			id: webhookId,
			source: "cc",
			sourceId: payload.id.toString(),
			type: payload.model?.toLowerCase() === "appointment" ? "appointment" : "patient",
			payload: payload,
		});

		logInfo(`CC webhook stored in queue: ${webhookId}`);

		return c.json(
			{
				status: "success",
				message: "CC webhook stored in queue",
				metadata: {
					timestamp,
					webhookId,
				},
			},
			200,
		);
	} catch (error) {
		return c.json(
			{
				status: "error",
				message: "Failed to store CC webhook in queue",
				metadata: {
					timestamp,
				},
			},
			500,
		);
	}
}

export async function queueAPWebhookHandler(c: Context): Promise<Response> {
	const timestamp = new Date().toISOString();

	try {
		logInfo("Received AP webhook for queue");

		const payload = await c.req.json();
		
		const db = getDb();
		const webhookId = crypto.randomUUID();
		
		// Validate required fields
		if (!payload.contact_id) {
			throw new Error("AP webhook payload missing required 'contact_id' field");
		}

		await db.insert(webhookQueue).values({
			id: webhookId,
			source: "ap",
			sourceId: payload.contact_id,
			type: payload.calendar ? "appointment" : "patient",
			payload: payload,
		});

		logInfo(`AP webhook stored in queue: ${webhookId}`);

		return c.json(
			{
				status: "success",
				message: "AP webhook stored in queue",
				metadata: {
					timestamp,
					webhookId,
				},
			},
			200,
		);
	} catch (error) {
		return c.json(
			{
				status: "error",
				message: "Failed to store AP webhook in queue",
				metadata: {
					timestamp,
				},
			},
			500,
		);
	}
}

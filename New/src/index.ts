import {
	adminCustom<PERSON>ield<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
	apContactWebhookHandler,
	ccWebhookHandler,
	cfHandler,
	handleErrorCleanup,
} from "@handlers";
import { queueCCWebhookHandler, queueAPWebhookHandler } from "@/handlers/queueWebhookHandler";
import { Hono } from "hono";
import { contextStorage } from "hono/context-storage";
import { requestId } from "hono/request-id";
import { logError } from "@/utils/logger";

const app = new Hono<Env>();
app.use(contextStorage());
app.use("*", requestId());

app.onError((err, c) => {
	const requestId = c.get("requestId") || "unknown";
	logError("Unhandled application error", err);
	return c.json(
		{
			message: "Internal Server Error",
			requestId,
			timestamp: new Date().toISOString(),
		},
		500,
	);
});

// Health check endpoints
app.get("/", (c) => c.text("DermaCare Data Sync Service - OK"));
app.get("/health", (c) => c.text("DermaCare Data Sync Service - OK"));

app.get("/cf", cfHandler);

// Webhook endpoints (store in queue)
app.post("/webhooks/ap", queueAPWebhookHandler);
app.post("/webhooks/cc", queueCCWebhookHandler);

// Internal webhook endpoints (replicas)
app.post("/internal-webhook/ap", apContactWebhookHandler);
app.post("/internal-webhook/cc", ccWebhookHandler);

// Admin endpoints
app.get("/admin/cleanup-errors", handleErrorCleanup);
app.post(
	"/admin/custom-fields-sync/:id/:platform",
	adminCustomFieldsSyncHandler,
);

export default app;

/**
 * Webhook Queue Process Management
 *
 * Provides a comprehensive class for managing webhook queue operations with
 * state management, database integration, and Hono context support. Handles
 * webhook lifecycle from creation to completion with proper error handling
 * and thread-safety considerations.
 *
 * @fileoverview Webhook queue process management class
 * @version 1.0.0
 * @since 2024-08-04
 */

import { dbSchema, getDb } from "@database";
import type { WebhookQueue } from "@database/schema";
import type { APContactCreationWebhookPayload } from "@/processors/apWebhook";
import type { CCWebhookPayload } from "@/processors/ccWebhook";
import { eq, and } from "drizzle-orm";
import { getRequestId } from "@/utils/getRequestId";
import { logDebug, logError, logInfo } from "@/utils/logger";
import { getContext } from "hono/context-storage";

/**
 * Process class for webhook queue management
 *
 * Manages webhook queue operations with state binding and lifecycle management.
 * After calling `add()` or `inProcess()`, the instance is bound to a specific
 * webhook record and subsequent operations work on that record.
 *
 * @example
 * ```typescript
 * // Add new webhook to queue
 * const process = new Process();
 * await process.add(webhookPayload);
 * await process.processing();
 * await process.complete();
 *
 * // Work with existing webhook
 * const process = new Process();
 * await process.inProcess("contact_123", "patient");
 * await process.processing();
 * // ... do work ...
 * await process.complete();
 * ```
 */
export class Process {
	private webhookRecord?: WebhookQueue;
	private readonly db = getDb();

	/**
	 * Add a new webhook to the queue
	 *
	 * Creates a new webhook queue record and binds this instance to it.
	 * The webhook starts in "pending" status.
	 *
	 * @param payload - Webhook payload (AP or CC)
	 * @throws {Error} When payload is invalid or database operation fails
	 */
	async add(payload: APContactCreationWebhookPayload | CCWebhookPayload): Promise<void> {
		const requestId = getRequestId();
		
		try {
			const sourceInfo = this.extractSourceInfo(payload);
			
			const webhookId = crypto.randomUUID();
			const now = new Date();
			
			const newRecord = {
				id: webhookId,
				source: sourceInfo.source,
				sourceId: sourceInfo.sourceId,
				type: sourceInfo.type as "patient" | "appointment",
				payload: payload,
				status: "pending" as const,
				retryCount: 0,
				maxRetries: 3,
				createdAt: now,
				updatedAt: now,
			};

			await this.db.insert(dbSchema.webhookQueue).values(newRecord);
			
			// Fetch the created record to bind to this instance
			const [created] = await this.db
				.select()
				.from(dbSchema.webhookQueue)
				.where(eq(dbSchema.webhookQueue.id, webhookId));
				
			this.webhookRecord = created;
			
			logInfo(`Webhook added to queue: ${webhookId} (${sourceInfo.source}:${sourceInfo.sourceId})`);
		} catch (error) {
			logError(`Failed to add webhook to queue: ${error}`, {
				type: "webhook_queue_add",
				data: { requestId, error },
			});
			throw new Error(`Failed to add webhook to queue: ${error}`);
		}
	}

	/**
	 * Bind to an existing webhook in the queue
	 *
	 * Finds and binds this instance to an existing webhook record by sourceId and type.
	 * The webhook must exist and be in a valid state for processing.
	 *
	 * @param sourceId - Source ID (AP contact/appointment ID or CC entity ID)
	 * @param type - Webhook type (default: "patient")
	 * @throws {Error} When webhook not found or database operation fails
	 */
	async inProcess(sourceId: string, type: "patient" | "appointment" = "patient"): Promise<void> {
		const requestId = getRequestId();
		
		try {
			const [record] = await this.db
				.select()
				.from(dbSchema.webhookQueue)
				.where(and(
					eq(dbSchema.webhookQueue.sourceId, sourceId),
					eq(dbSchema.webhookQueue.type, type)
				))
				.limit(1);

			if (!record) {
				throw new Error(`Webhook not found for sourceId: ${sourceId}, type: ${type}`);
			}

			this.webhookRecord = record;
			
			logDebug(`Process bound to webhook: ${record.id} (${sourceId}:${type})`);
		} catch (error) {
			logError(`Failed to bind to webhook: ${error}`, {
				type: "webhook_queue_bind",
				data: { requestId, sourceId, type, error },
			});
			throw new Error(`Failed to bind to webhook: ${error}`);
		}
	}

	/**
	 * Remove the webhook from the queue
	 *
	 * Permanently deletes the bound webhook record from the database.
	 * This operation cannot be undone.
	 *
	 * @throws {Error} When not bound to a webhook or database operation fails
	 */
	async remove(): Promise<void> {
		this.ensureBound();
		const requestId = getRequestId();
		
		try {
			await this.db
				.delete(dbSchema.webhookQueue)
				.where(eq(dbSchema.webhookQueue.id, this.webhookRecord!.id));
				
			logInfo(`Webhook removed from queue: ${this.webhookRecord!.id}`);
			this.webhookRecord = undefined;
		} catch (error) {
			logError(`Failed to remove webhook: ${error}`, {
				type: "webhook_queue_remove",
				data: { requestId, webhookId: this.webhookRecord!.id, error },
			});
			throw new Error(`Failed to remove webhook: ${error}`);
		}
	}

	/**
	 * Mark webhook for retry
	 *
	 * Keeps the webhook in pending status, increments retry count,
	 * and optionally saves a retry reason.
	 *
	 * @param message - Optional retry reason message
	 * @throws {Error} When not bound to a webhook or database operation fails
	 */
	async tryAgain(message?: string): Promise<void> {
		this.ensureBound();
		const requestId = getRequestId();
		
		try {
			const now = new Date();
			
			await this.db
				.update(dbSchema.webhookQueue)
				.set({
					status: "pending",
					retryCount: this.webhookRecord!.retryCount + 1,
					lastRetryAttemptAt: now,
					lastRetryReason: message || null,
					updatedAt: now,
				})
				.where(eq(dbSchema.webhookQueue.id, this.webhookRecord!.id));
				
			// Update local record
			this.webhookRecord = {
				...this.webhookRecord!,
				status: "pending",
				retryCount: this.webhookRecord!.retryCount + 1,
				lastRetryAttemptAt: now,
				lastRetryReason: message || null,
				updatedAt: now,
			};
			
			logInfo(`Webhook marked for retry: ${this.webhookRecord.id} (attempt ${this.webhookRecord.retryCount})`);
		} catch (error) {
			logError(`Failed to mark webhook for retry: ${error}`, {
				type: "webhook_queue_retry",
				data: { requestId, webhookId: this.webhookRecord!.id, error },
			});
			throw new Error(`Failed to mark webhook for retry: ${error}`);
		}
	}

	/**
	 * Mark webhook as failed
	 *
	 * Sets the webhook status to "failed" with error message and optional details.
	 * This is a terminal state for the webhook.
	 *
	 * @param message - Error message describing the failure
	 * @param details - Optional error details object or Error instance
	 * @throws {Error} When not bound to a webhook or database operation fails
	 */
	async failed(message: string, details?: Record<string, unknown> | Error): Promise<void> {
		this.ensureBound();
		const requestId = getRequestId();

		try {
			const now = new Date();

			await this.db
				.update(dbSchema.webhookQueue)
				.set({
					status: "failed" as const,
					errorMessage: message,
					errorDetails: (details instanceof Error ? { name: details.name, message: details.message, stack: details.stack } : details) || null,
					processingCompletedAt: now,
					updatedAt: now,
				})
				.where(eq(dbSchema.webhookQueue.id, this.webhookRecord!.id));

			// Update local record
			this.webhookRecord = {
				...this.webhookRecord!,
				status: "failed",
				errorMessage: message,
				errorDetails: (details instanceof Error ? { name: details.name, message: details.message, stack: details.stack } : details) || null,
				processingCompletedAt: now,
				updatedAt: now,
			};

			logError(`Webhook marked as failed: ${this.webhookRecord!.id} - ${message}`, {
				type: "webhook_queue_failed",
				data: { requestId, webhookId: this.webhookRecord!.id, details },
			});
		} catch (error) {
			logError(`Failed to mark webhook as failed: ${error}`, {
				type: "webhook_queue_fail_update",
				data: { requestId, webhookId: this.webhookRecord!.id, error },
			});
			throw new Error(`Failed to mark webhook as failed: ${error}`);
		}
	}

	/**
	 * Mark webhook as completed
	 *
	 * Sets the webhook status to "completed" indicating successful processing.
	 * This is a terminal state for the webhook.
	 *
	 * @throws {Error} When not bound to a webhook or database operation fails
	 */
	async complete(): Promise<void> {
		this.ensureBound();
		const requestId = getRequestId();
		
		try {
			const now = new Date();
			
			await this.db
				.update(dbSchema.webhookQueue)
				.set({
					status: "completed",
					processingCompletedAt: now,
					updatedAt: now,
				})
				.where(eq(dbSchema.webhookQueue.id, this.webhookRecord!.id));
				
			// Update local record
			this.webhookRecord = {
				...this.webhookRecord!,
				status: "completed",
				processingCompletedAt: now,
				updatedAt: now,
			};
			
			logInfo(`Webhook completed: ${this.webhookRecord.id}`);
		} catch (error) {
			logError(`Failed to mark webhook as completed: ${error}`, {
				type: "webhook_queue_complete",
				data: { requestId, webhookId: this.webhookRecord!.id, error },
			});
			throw new Error(`Failed to mark webhook as completed: ${error}`);
		}
	}

	/**
	 * Mark webhook as currently being processed
	 *
	 * Sets the webhook status to "processing" and records the processing start time.
	 * This indicates active processing is in progress.
	 *
	 * @throws {Error} When not bound to a webhook or database operation fails
	 */
	async processing(): Promise<void> {
		this.ensureBound();
		const requestId = getRequestId();

		try {
			const now = new Date();

			await this.db
				.update(dbSchema.webhookQueue)
				.set({
					status: "processing",
					processingStartedAt: now,
					updatedAt: now,
				})
				.where(eq(dbSchema.webhookQueue.id, this.webhookRecord!.id));

			// Update local record
			this.webhookRecord = {
				...this.webhookRecord!,
				status: "processing",
				processingStartedAt: now,
				updatedAt: now,
			};

			logDebug(`Webhook processing started: ${this.webhookRecord.id}`);
		} catch (error) {
			logError(`Failed to mark webhook as processing: ${error}`, {
				type: "webhook_queue_processing",
				data: { requestId, webhookId: this.webhookRecord!.id, error },
			});
			throw new Error(`Failed to mark webhook as processing: ${error}`);
		}
	}

	/**
	 * Get the current webhook record
	 *
	 * Returns the bound webhook record for inspection.
	 *
	 * @returns The bound webhook record or undefined if not bound
	 */
	getRecord(): WebhookQueue | undefined {
		return this.webhookRecord;
	}

	/**
	 * Check if the instance is bound to a webhook
	 *
	 * @returns True if bound to a webhook record
	 */
	isBound(): boolean {
		return this.webhookRecord !== undefined;
	}

	/**
	 * Ensure the instance is bound to a webhook
	 *
	 * @throws {Error} When not bound to a webhook
	 */
	private ensureBound(): void {
		if (!this.webhookRecord) {
			throw new Error("Process not bound to a webhook. Call add() or inProcess() first.");
		}
	}

	/**
	 * Extract source information from webhook payload
	 *
	 * Determines the source, sourceId, and type from the payload structure.
	 *
	 * @param payload - Webhook payload
	 * @returns Source information object
	 * @throws {Error} When payload format is invalid
	 */
	private extractSourceInfo(payload: APContactCreationWebhookPayload | CCWebhookPayload): {
		source: string;
		sourceId: string;
		type: string;
	} {
		if (this.isAPPayload(payload)) {
			return {
				source: "ap",
				sourceId: payload.contact_id,
				type: payload.calendar ? "appointment" : "patient",
			};
		} else if (this.isCCPayload(payload)) {
			return {
				source: "cc",
				sourceId: payload.id.toString(),
				type: payload.model.toLowerCase() === "appointment" ? "appointment" : "patient",
			};
		} else {
			throw new Error("Invalid payload format: unable to determine source");
		}
	}

	/**
	 * Type guard for AP webhook payload
	 *
	 * @param payload - Payload to check
	 * @returns True if payload is AP webhook payload
	 */
	private isAPPayload(payload: unknown): payload is APContactCreationWebhookPayload {
		return (
			typeof payload === "object" &&
			payload !== null &&
			"contact_id" in payload &&
			typeof (payload as Record<string, unknown>).contact_id === "string"
		);
	}

	/**
	 * Type guard for CC webhook payload
	 *
	 * @param payload - Payload to check
	 * @returns True if payload is CC webhook payload
	 */
	private isCCPayload(payload: unknown): payload is CCWebhookPayload {
		return (
			typeof payload === "object" &&
			payload !== null &&
			"event" in payload &&
			"model" in payload &&
			"id" in payload &&
			typeof (payload as Record<string, unknown>).id === "number"
		);
	}
}

/**
 * Get Process instance from Hono context
 *
 * Retrieves or creates a Process instance attached to the current Hono context.
 * This allows sharing Process instances across request handlers.
 *
 * @returns Process instance from context
 */
export const useProcess = (): Process => {
	try {
		const c = getContext<Env>();

		// Try to get existing process from context variables
		const variables = c.var as Record<string, unknown>;
		let process = variables.process as Process | undefined;

		if (!process) {
			process = new Process();
			// Store in context variables
			variables.process = process;
		}

		return process;
	} catch {
		// Fallback if context is not available
		return new Process();
	}
};

/**
 * Hono middleware to attach Process to context
 *
 * Middleware that automatically creates and attaches a Process instance
 * to the Hono context for use in request handlers.
 *
 * @example
 * ```typescript
 * app.use("*", processMiddleware());
 *
 * app.post("/webhook", async (c) => {
 *   const process = useProcess();
 *   await process.add(payload);
 *   // ... handle webhook
 * });
 * ```
 */
export const processMiddleware = () => {
	return async (c: { var: Record<string, unknown> }, next: () => Promise<void>) => {
		c.var.process = new Process();
		await next();
	};
};
